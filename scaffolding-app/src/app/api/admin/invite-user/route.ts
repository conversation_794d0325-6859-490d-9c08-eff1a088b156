import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { email, name, role } = await request.json()

    // Validate required fields
    if (!email || !name || !role) {
      return NextResponse.json(
        { error: 'Email, name, and role are required' },
        { status: 400 }
      )
    }

    // Validate role
    if (!['manager', 'worker'].includes(role)) {
      return NextResponse.json(
        { error: 'Role must be either "manager" or "worker"' },
        { status: 400 }
      )
    }

    // Create Supabase client with service role
    const supabase = createSupabaseServerClient()

    // Send invitation email using Supabase Auth Admin API
    const { data, error } = await supabase.auth.admin.inviteUserByEmail(email, {
      data: {
        name,
        role
      }
    })

    if (error) {
      console.error('Error inviting user:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Invitation sent successfully',
      user: data.user
    })

  } catch (error) {
    console.error('Unexpected error inviting user:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
