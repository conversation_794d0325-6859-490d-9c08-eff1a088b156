{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\nimport type { Database } from './database.types.js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nif (!supabaseUrl || !supabaseAnonKey) {\n  throw new Error('Missing Supabase environment variables')\n}\n\n// Singleton browser client to avoid multiple instances\nlet browserClient: ReturnType<typeof createBrowserClient<Database>> | null = null\n\n// Browser client for client-side operations with proper auth handling\nexport function createSupabaseClient() {\n  // Always use browser client for consistency in client-side components\n  if (!browserClient) {\n    browserClient = createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {\n      auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n      },\n      isSingleton: true\n    })\n  }\n\n  return browserClient\n}\n\n// For server-side operations\nexport function createSupabaseServerClient() {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY\n\n  if (!serviceRoleKey) {\n    throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable')\n  }\n\n  return createClient<Database>(\n    supabaseUrl,\n    serviceRoleKey,\n    {\n      auth: {\n        autoRefreshToken: false,\n        persistSession: false\n      }\n    }\n  )\n}\n\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAGA,MAAM;AACN,MAAM;AAEN,uCAAsC;;AAEtC;AAEA,uDAAuD;AACvD,IAAI,gBAAyE;AAGtE,SAAS;IACd,sEAAsE;IACtE,IAAI,CAAC,eAAe;QAClB,gBAAgB,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAY,aAAa,iBAAiB;YAC1E,MAAM;gBACJ,kBAAkB;gBAClB,gBAAgB;gBAChB,oBAAoB;YACtB;YACA,aAAa;QACf;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,iBAAiB,QAAQ,GAAG,CAAC,yBAAyB;IAE5D,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAChB,aACA,gBACA;QACE,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/app/api/admin/invite-user/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createSupabaseServerClient } from '@/lib/supabase'\nimport { createClient } from '@supabase/supabase-js'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Parse the request body first\n    const { email, name, role } = await request.json()\n\n    // Get the authorization header\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader) {\n      return NextResponse.json(\n        { error: 'Authorization header required' },\n        { status: 401 }\n      )\n    }\n\n    // Verify the user is authenticated by checking the JWT token\n    const token = authHeader.replace('Bearer ', '')\n    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\n    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n    const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey)\n    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser(token)\n\n    if (authError || !user) {\n      return NextResponse.json(\n        { error: 'Invalid or expired token' },\n        { status: 401 }\n      )\n    }\n\n    // Validate required fields\n    if (!email || !name || !role) {\n      return NextResponse.json(\n        { error: 'Email, name, and role are required' },\n        { status: 400 }\n      )\n    }\n\n    // Validate role\n    if (!['manager', 'worker'].includes(role)) {\n      return NextResponse.json(\n        { error: 'Role must be either \"manager\" or \"worker\"' },\n        { status: 400 }\n      )\n    }\n\n    // Create Supabase client with service role\n    const supabase = createSupabaseServerClient()\n\n    // Send invitation email using Supabase Auth Admin API\n    const { data, error } = await supabase.auth.admin.inviteUserByEmail(email, {\n      data: {\n        name,\n        role\n      }\n    })\n\n    if (error) {\n      console.error('Error inviting user:', error)\n      return NextResponse.json(\n        { error: error.message },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: 'Invitation sent successfully',\n      user: data.user\n    })\n\n  } catch (error) {\n    console.error('Unexpected error inviting user:', error)\n    return NextResponse.json(\n      { error: 'An unexpected error occurred' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,+BAA+B;QAC/B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEhD,+BAA+B;QAC/B,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,6DAA6D;QAC7D,MAAM,QAAQ,WAAW,OAAO,CAAC,WAAW;QAC5C,MAAM;QACN,MAAM;QAEN,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;QAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC;QAE7E,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,IAAI,CAAC;YAAC;YAAW;SAAS,CAAC,QAAQ,CAAC,OAAO;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4C,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,2CAA2C;QAC3C,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,6BAA0B,AAAD;QAE1C,sDAAsD;QACtD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,OAAO;YACzE,MAAM;gBACJ;gBACA;YACF;QACF;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,MAAM,OAAO;YAAC,GACvB;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM,KAAK,IAAI;QACjB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA+B,GACxC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}